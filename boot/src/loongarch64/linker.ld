/* LoongArch 64-bit Linker Script */

OUTPUT_ARCH(loongarch64)
ENTRY(_start)

MEMORY
{
    /* LoongArch memory layout */
    RAM : ORIGIN = 0x90000000, LENGTH = 128M
}

SECTIONS
{
    . = 0x90000000;
    
    .text : {
        KEEP(*(.text.boot))
        *(.text .text.*)
    } > RAM
    
    .rodata : {
        *(.rodata .rodata.*)
    } > RAM
    
    .data : {
        *(.data .data.*)
    } > RAM
    
    .bss : {
        __bss_start = .;
        *(.bss .bss.*)
        *(COMMON)
        __bss_end = .;
    } > RAM
    
    . = ALIGN(4096);
    __kernel_end = .;
    
    /DISCARD/ : {
        *(.comment)
        *(.gnu*)
        *(.note*)
        *(.eh_frame*)
    }
}
